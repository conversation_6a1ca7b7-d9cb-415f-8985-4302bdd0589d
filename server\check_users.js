import Database from 'better-sqlite3';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const DB_PATH = join(__dirname, 'data', 'app.db');

try {
    const db = new Database(DB_PATH);
    
    console.log('=== 检查用户登录信息 ===');
    const users = db.prepare("SELECT id, username, email, status, password_hash FROM users WHERE username = 'admin'").all();
    console.log('Admin用户信息:', users);
    
    // 检查所有用户的基本信息
    const allUsers = db.prepare("SELECT id, username, email, status FROM users").all();
    console.log('\n所有用户:', allUsers);
    
    db.close();
} catch (error) {
    console.error('检查用户时出错:', error);
}
