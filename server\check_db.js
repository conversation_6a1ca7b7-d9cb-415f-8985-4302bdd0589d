import Database from 'better-sqlite3';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const DB_PATH = join(__dirname, 'data', 'app.db');

try {
    const db = new Database(DB_PATH);
    
    console.log('检查 chat_messages 表结构:');
    const schema = db.prepare("SELECT sql FROM sqlite_master WHERE type='table' AND name='chat_messages'").get();
    console.log(schema ? schema.sql : '表不存在');
    
    console.log('\n检查表中的列:');
    const columns = db.prepare("PRAGMA table_info(chat_messages)").all();
    console.log(columns);
    
    db.close();
} catch (error) {
    console.error('检查数据库时出错:', error);
}
