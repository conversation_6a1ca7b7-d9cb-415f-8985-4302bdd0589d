<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>聊天历史调试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .debug-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .error { color: red; }
        .success { color: green; }
        .info { color: blue; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>聊天历史调试页面</h1>
    
    <div class="debug-section">
        <h2>1. 登录状态检查</h2>
        <div id="authStatus">检查中...</div>
    </div>
    
    <div class="debug-section">
        <h2>2. API调用测试</h2>
        <button onclick="testChatSessionsAPI()">测试聊天会话API</button>
        <div id="apiResult">点击按钮测试API</div>
    </div>
    
    <div class="debug-section">
        <h2>3. 聊天历史渲染测试</h2>
        <div id="chatHistoryTest" style="border: 1px solid #ddd; min-height: 100px; padding: 10px;">
            <!-- 这里会显示渲染的聊天历史 -->
        </div>
    </div>

    <script>
        // 检查登录状态
        async function checkAuth() {
            try {
                const response = await fetch('/api/auth/me', { credentials: 'include' });
                const authStatus = document.getElementById('authStatus');
                
                if (response.ok) {
                    const data = await response.json();
                    authStatus.innerHTML = `<span class="success">✅ 已登录: ${data.user.username}</span>`;
                    return data.user;
                } else {
                    authStatus.innerHTML = `<span class="error">❌ 未登录 (${response.status})</span>`;
                    return null;
                }
            } catch (error) {
                document.getElementById('authStatus').innerHTML = `<span class="error">❌ 检查登录状态失败: ${error.message}</span>`;
                return null;
            }
        }
        
        // 测试聊天会话API
        async function testChatSessionsAPI() {
            const resultDiv = document.getElementById('apiResult');
            resultDiv.innerHTML = '<span class="info">🔄 正在调用API...</span>';
            
            try {
                const response = await fetch('/api/chat/sessions', { credentials: 'include' });
                
                if (response.ok) {
                    const data = await response.json();
                    resultDiv.innerHTML = `
                        <div class="success">✅ API调用成功</div>
                        <div><strong>返回数据:</strong></div>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                    
                    // 测试渲染
                    renderChatHistoryTest(data.sessions);
                } else {
                    const errorText = await response.text();
                    resultDiv.innerHTML = `<span class="error">❌ API调用失败: ${response.status} - ${errorText}</span>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<span class="error">❌ API调用异常: ${error.message}</span>`;
            }
        }
        
        // 测试聊天历史渲染
        function renderChatHistoryTest(sessions) {
            const testDiv = document.getElementById('chatHistoryTest');
            
            if (!sessions || sessions.length === 0) {
                testDiv.innerHTML = '<div style="color: gray;">暂无聊天历史</div>';
                return;
            }
            
            const historyHTML = sessions.map(session => `
                <div style="border: 1px solid #eee; margin: 5px 0; padding: 10px; border-radius: 5px;">
                    <div><strong>${escapeHtml(session.title)}</strong></div>
                    <div style="font-size: 12px; color: #666;">
                        ${session.message_count || 0} 条消息 • ${formatDate(session.updated_at)}
                    </div>
                </div>
            `).join('');
            
            testDiv.innerHTML = historyHTML;
        }
        
        // 工具函数
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }
        
        function formatDate(dateStr) {
            return new Date(dateStr).toLocaleString('zh-CN');
        }
        
        // 页面加载时检查登录状态
        window.onload = function() {
            checkAuth();
        };
    </script>
</body>
</html>
