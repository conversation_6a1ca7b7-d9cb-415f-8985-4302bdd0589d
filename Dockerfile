# 使用官方Node.js运行时作为基础镜像
FROM node:18-alpine

# 设置工作目录
WORKDIR /app

# 复制package.json和package-lock.json
COPY server/package*.json ./

# 安装依赖
RUN npm install --production && npm cache clean --force

# 复制应用代码
COPY server/ .
COPY web/ ./web/

# 创建数据目录
RUN mkdir -p data logs

# 设置权限
RUN chown -R node:node /app
USER node

# 暴露端口
EXPOSE 3001

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD node -e "require('http').get('http://localhost:3001/healthz', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"

# 启动应用
CMD ["npm", "start"]
