import fetch from 'node-fetch';

// 测试聊天会话API
async function testChatSessionsAPI() {
    try {
        console.log('=== 测试 /api/chat/sessions API ===');
        
        // 首先登录获取cookie
        const loginResponse = await fetch('http://localhost:3001/api/auth/login', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ username: 'admin', password: 'admin' })
        });
        
        if (!loginResponse.ok) {
            console.error('登录失败:', loginResponse.status);
            return;
        }
        
        const cookies = loginResponse.headers.get('set-cookie');
        console.log('登录成功，获取到cookies');
        
        // 调用聊天会话API
        const sessionsResponse = await fetch('http://localhost:3001/api/chat/sessions', {
            headers: { 
                'Cookie': cookies 
            }
        });
        
        if (sessionsResponse.ok) {
            const data = await sessionsResponse.json();
            console.log('API响应成功:');
            console.log('响应数据:', JSON.stringify(data, null, 2));
            console.log('sessions数组长度:', data.sessions ? data.sessions.length : 'undefined');
            
            if (data.sessions && data.sessions.length > 0) {
                console.log('第一个会话的结构:', data.sessions[0]);
            }
        } else {
            console.error('API调用失败:', sessionsResponse.status, await sessionsResponse.text());
        }
        
    } catch (error) {
        console.error('测试过程中出错:', error);
    }
}

testChatSessionsAPI();
