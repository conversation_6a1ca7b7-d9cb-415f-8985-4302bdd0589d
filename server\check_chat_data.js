import Database from 'better-sqlite3';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const DB_PATH = join(__dirname, 'data', 'app.db');

try {
    const db = new Database(DB_PATH);
    
    console.log('=== 检查用户数据 ===');
    const users = db.prepare("SELECT id, username, email, status FROM users").all();
    console.log('用户列表:', users);
    
    console.log('\n=== 检查聊天会话数据 ===');
    const sessions = db.prepare("SELECT * FROM chat_sessions ORDER BY updated_at DESC LIMIT 10").all();
    console.log('最近的聊天会话:', sessions);
    
    console.log('\n=== 检查聊天消息数据 ===');
    const messages = db.prepare("SELECT * FROM chat_messages ORDER BY created_at DESC LIMIT 10").all();
    console.log('最近的聊天消息:', messages);
    
    console.log('\n=== 检查每个用户的会话统计 ===');
    const userStats = db.prepare(`
        SELECT 
            u.id, u.username,
            COUNT(DISTINCT cs.id) as session_count,
            COUNT(cm.id) as message_count
        FROM users u
        LEFT JOIN chat_sessions cs ON u.id = cs.user_id AND cs.is_active = 1
        LEFT JOIN chat_messages cm ON cs.id = cm.session_id
        GROUP BY u.id, u.username
    `).all();
    console.log('用户统计:', userStats);
    
    db.close();
} catch (error) {
    console.error('检查数据库时出错:', error);
}
