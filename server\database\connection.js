import Database from 'better-sqlite3';
import { fileURLToPath } from 'url';
import { dirname, join, resolve } from 'path';
import { existsSync, mkdirSync, readFileSync } from 'fs';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// 获取项目根目录
const PROJECT_ROOT = resolve(__dirname, '..', '..');

// 数据库文件路径 - 支持环境变量配置
function getDatabasePath() {
    const envPath = process.env.DATABASE_PATH;
    if (envPath) {
        // 如果是绝对路径，直接使用
        if (resolve(envPath) === envPath) {
            return envPath;
        }
        // 如果是相对路径，相对于项目根目录
        return resolve(PROJECT_ROOT, envPath);
    }
    // 默认路径
    return join(__dirname, '..', 'data', 'app.db');
}

const DB_PATH = getDatabasePath();
const SCHEMA_PATH = join(__dirname, 'schema.sql');

// 验证和创建数据库目录
function ensureDatabaseDirectory() {
    const dataDir = dirname(DB_PATH);

    try {
        if (!existsSync(dataDir)) {
            mkdirSync(dataDir, { recursive: true });
            console.log(`✅ 创建数据库目录: ${dataDir}`);
        }

        // 检查目录权限（可选）
        try {
            const testFile = join(dataDir, '.write-test');
            import('fs').then(fs => {
                fs.writeFileSync(testFile, 'test');
                fs.unlinkSync(testFile);
            });
        } catch (permError) {
            console.warn(`⚠️ 数据库目录权限检查失败，但继续运行: ${permError.message}`);
        }

        return true;
    } catch (error) {
        console.error(`❌ 数据库目录创建或权限检查失败: ${error.message}`);
        return false;
    }
}

// 确保数据目录存在
if (!ensureDatabaseDirectory()) {
    throw new Error('数据库目录初始化失败');
}

// 创建数据库连接
const db = new Database(DB_PATH);

// 启用外键约束
db.pragma('foreign_keys = ON');

// 设置WAL模式以提高并发性能
db.pragma('journal_mode = WAL');

// 数据库迁移函数
function runMigrations() {
    try {
        // 检查是否需要添加 sources 列
        const columns = db.prepare("PRAGMA table_info(chat_messages)").all();
        const hasSourcesColumn = columns.some(col => col.name === 'sources');

        if (!hasSourcesColumn) {
            console.log('🔄 添加 sources 列到 chat_messages 表...');
            db.exec('ALTER TABLE chat_messages ADD COLUMN sources TEXT');
            console.log('✅ sources 列添加成功');
        }

        return true;
    } catch (error) {
        console.error('❌ 数据库迁移失败:', error);
        return false;
    }
}

// 初始化数据库表结构
export function initializeDatabase() {
    try {
        console.log('🔄 正在初始化数据库...');

        // 先运行数据库迁移（针对现有表）
        runMigrations();

        // 读取并执行SQL schema（会创建不存在的表和索引）
        const schema = readFileSync(SCHEMA_PATH, 'utf8');
        db.exec(schema);

        console.log('✅ 数据库初始化完成');
        return true;
    } catch (error) {
        console.error('❌ 数据库初始化失败:', error);
        return false;
    }
}

// 数据库健康检查
export function checkDatabaseHealth() {
    try {
        const result = db.prepare('SELECT 1 as health').get();
        return result.health === 1;
    } catch (error) {
        console.error('数据库健康检查失败:', error);
        return false;
    }
}

// 获取数据库统计信息
export function getDatabaseStats() {
    try {
        const stats = {
            users: db.prepare('SELECT COUNT(*) as count FROM users').get().count,
            configs: db.prepare('SELECT COUNT(*) as count FROM configs').get().count,
            chatSessions: db.prepare('SELECT COUNT(*) as count FROM chat_sessions').get().count,
            chatMessages: db.prepare('SELECT COUNT(*) as count FROM chat_messages').get().count,
        };
        return stats;
    } catch (error) {
        console.error('获取数据库统计信息失败:', error);
        return null;
    }
}

// 获取数据库路径信息
export function getDatabaseInfo() {
    return {
        path: DB_PATH,
        relativePath: DB_PATH.replace(PROJECT_ROOT, '').replace(/\\/g, '/'),
        exists: existsSync(DB_PATH),
        size: existsSync(DB_PATH) ? 'N/A' : 0,
        directory: dirname(DB_PATH)
    };
}

// 关闭数据库连接
export function closeDatabase() {
    try {
        db.close();
        console.log('✅ 数据库连接已关闭');
    } catch (error) {
        console.error('❌ 关闭数据库连接失败:', error);
    }
}

// 导出数据库实例
export { db };

// 设置进程退出时自动关闭数据库的函数
export function setupDatabaseCleanup() {
    process.on('exit', closeDatabase);
    process.on('SIGINT', () => {
        closeDatabase();
        process.exit(0);
    });
    process.on('SIGTERM', () => {
        closeDatabase();
        process.exit(0);
    });
}
